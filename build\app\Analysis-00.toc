(['C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\app.py'],
 ['C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService'],
 [],
 [('C:\\Python38-32\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('c:\\python38-32\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('c:\\python38-32\\lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('static\\css\\bootstrap.min.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\css\\bootstrap.min.css',
   'DATA'),
  ('static\\css\\daterangepicker.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\css\\daterangepicker.css',
   'DATA'),
  ('static\\css\\demo.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\css\\demo.css',
   'DATA'),
  ('static\\css\\desktop.ini',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\css\\desktop.ini',
   'DATA'),
  ('static\\css\\main.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\css\\main.css',
   'DATA'),
  ('static\\fireworks.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\fireworks.html',
   'DATA'),
  ('static\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\index.html',
   'DATA'),
  ('static\\js\\bootstrap.bundle.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\bootstrap.bundle.min.js',
   'DATA'),
  ('static\\js\\daterangepicker.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\daterangepicker.min.js',
   'DATA'),
  ('static\\js\\echarts.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\echarts.min.js',
   'DATA'),
  ('static\\js\\jquery.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\jquery.min.js',
   'DATA'),
  ('static\\js\\moment.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\moment.min.js',
   'DATA'),
  ('static\\js\\xlsx.full.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\xlsx.full.min.js',
   'DATA')],
 '3.8.8 (tags/v3.8.8:024d805, Feb 19 2021, 13:08:11) [MSC v.1928 32 bit '
 '(Intel)]',
 [('pyi_rth_pkgutil',
   'C:\\Python38-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Python38-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Python38-32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('app',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\app.py',
   'PYSOURCE')],
 [('subprocess', 'c:\\python38-32\\lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'c:\\python38-32\\lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'c:\\python38-32\\lib\\contextlib.py', 'PYMODULE'),
  ('threading', 'c:\\python38-32\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'c:\\python38-32\\lib\\_threading_local.py', 'PYMODULE'),
  ('signal', 'c:\\python38-32\\lib\\signal.py', 'PYMODULE'),
  ('_strptime', 'c:\\python38-32\\lib\\_strptime.py', 'PYMODULE'),
  ('calendar', 'c:\\python38-32\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'c:\\python38-32\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'c:\\python38-32\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'c:\\python38-32\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'c:\\python38-32\\lib\\gettext.py', 'PYMODULE'),
  ('struct', 'c:\\python38-32\\lib\\struct.py', 'PYMODULE'),
  ('shutil', 'c:\\python38-32\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'c:\\python38-32\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'c:\\python38-32\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'c:\\python38-32\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'c:\\python38-32\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\python38-32\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\python38-32\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\python38-32\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc', 'c:\\python38-32\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('configparser', 'c:\\python38-32\\lib\\configparser.py', 'PYMODULE'),
  ('pathlib', 'c:\\python38-32\\lib\\pathlib.py', 'PYMODULE'),
  ('email', 'c:\\python38-32\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'c:\\python38-32\\lib\\email\\parser.py', 'PYMODULE'),
  ('email._policybase',
   'c:\\python38-32\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils', 'c:\\python38-32\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'c:\\python38-32\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('socket', 'c:\\python38-32\\lib\\socket.py', 'PYMODULE'),
  ('random', 'c:\\python38-32\\lib\\random.py', 'PYMODULE'),
  ('hashlib', 'c:\\python38-32\\lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'c:\\python38-32\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'c:\\python38-32\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'c:\\python38-32\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle', 'c:\\python38-32\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('string', 'c:\\python38-32\\lib\\string.py', 'PYMODULE'),
  ('bisect', 'c:\\python38-32\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'c:\\python38-32\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message', 'c:\\python38-32\\lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'c:\\python38-32\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'c:\\python38-32\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\python38-32\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\python38-32\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'c:\\python38-32\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'c:\\python38-32\\lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'c:\\python38-32\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'c:\\python38-32\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'c:\\python38-32\\lib\\getopt.py', 'PYMODULE'),
  ('quopri', 'c:\\python38-32\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'c:\\python38-32\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'c:\\python38-32\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'c:\\python38-32\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'c:\\python38-32\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.header', 'c:\\python38-32\\lib\\email\\header.py', 'PYMODULE'),
  ('email.base64mime',
   'c:\\python38-32\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset', 'c:\\python38-32\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'c:\\python38-32\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'c:\\python38-32\\lib\\email\\errors.py', 'PYMODULE'),
  ('csv', 'c:\\python38-32\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'c:\\python38-32\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'c:\\python38-32\\lib\\token.py', 'PYMODULE'),
  ('importlib.util', 'c:\\python38-32\\lib\\importlib\\util.py', 'PYMODULE'),
  ('tarfile', 'c:\\python38-32\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'c:\\python38-32\\lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'c:\\python38-32\\lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'c:\\python38-32\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'c:\\python38-32\\lib\\bz2.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\python38-32\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\python38-32\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\python38-32\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\python38-32\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\python38-32\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\python38-32\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'c:\\python38-32\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'c:\\python38-32\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\python38-32\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\python38-32\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'c:\\python38-32\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\python38-32\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\python38-32\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request', 'c:\\python38-32\\lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'c:\\python38-32\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'c:\\python38-32\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'c:\\python38-32\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'c:\\python38-32\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'c:\\python38-32\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'c:\\python38-32\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', 'c:\\python38-32\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'c:\\python38-32\\lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'c:\\python38-32\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response', 'c:\\python38-32\\lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'c:\\python38-32\\lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'c:\\python38-32\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'c:\\python38-32\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\python38-32\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\python38-32\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'c:\\python38-32\\lib\\http\\client.py', 'PYMODULE'),
  ('decimal', 'c:\\python38-32\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\python38-32\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'c:\\python38-32\\lib\\contextvars.py', 'PYMODULE'),
  ('numbers', 'c:\\python38-32\\lib\\numbers.py', 'PYMODULE'),
  ('hmac', 'c:\\python38-32\\lib\\hmac.py', 'PYMODULE'),
  ('tempfile', 'c:\\python38-32\\lib\\tempfile.py', 'PYMODULE'),
  ('multiprocessing.context',
   'c:\\python38-32\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\python38-32\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\python38-32\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\python38-32\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\python38-32\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\python38-32\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\python38-32\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'c:\\python38-32\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.wintypes', 'c:\\python38-32\\lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('ctypes._endian', 'c:\\python38-32\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\python38-32\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\python38-32\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\python38-32\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'c:\\python38-32\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\python38-32\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\python38-32\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\python38-32\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\python38-32\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'c:\\python38-32\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\python38-32\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\python38-32\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'c:\\python38-32\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'c:\\python38-32\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'c:\\python38-32\\lib\\zipimport.py', 'PYMODULE'),
  ('inspect', 'c:\\python38-32\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'c:\\python38-32\\lib\\ast.py', 'PYMODULE'),
  ('dis', 'c:\\python38-32\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'c:\\python38-32\\lib\\opcode.py', 'PYMODULE'),
  ('multiprocessing',
   'c:\\python38-32\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('tracemalloc', 'c:\\python38-32\\lib\\tracemalloc.py', 'PYMODULE'),
  ('stringprep', 'c:\\python38-32\\lib\\stringprep.py', 'PYMODULE'),
  ('typing', 'c:\\python38-32\\lib\\typing.py', 'PYMODULE'),
  ('_py_abc', 'c:\\python38-32\\lib\\_py_abc.py', 'PYMODULE'),
  ('pymysql.cursors',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('urllib.parse', 'c:\\python38-32\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('json', 'c:\\python38-32\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'c:\\python38-32\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'c:\\python38-32\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'c:\\python38-32\\lib\\json\\scanner.py', 'PYMODULE'),
  ('fnmatch', 'c:\\python38-32\\lib\\fnmatch.py', 'PYMODULE'),
  ('numpy',
   'c:\\python38-32\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\python38-32\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('__future__', 'c:\\python38-32\\lib\\__future__.py', 'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\python38-32\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\python38-32\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'c:\\python38-32\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('platform', 'c:\\python38-32\\lib\\platform.py', 'PYMODULE'),
  ('numpy.distutils',
   'c:\\python38-32\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('doctest', 'c:\\python38-32\\lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'c:\\python38-32\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'c:\\python38-32\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'c:\\python38-32\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server', 'c:\\python38-32\\lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'c:\\python38-32\\lib\\socketserver.py', 'PYMODULE'),
  ('html', 'c:\\python38-32\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'c:\\python38-32\\lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\python38-32\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'c:\\python38-32\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('tty', 'c:\\python38-32\\lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'c:\\python38-32\\lib\\sysconfig.py', 'PYMODULE'),
  ('glob', 'c:\\python38-32\\lib\\glob.py', 'PYMODULE'),
  ('code', 'c:\\python38-32\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'c:\\python38-32\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'c:\\python38-32\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'c:\\python38-32\\lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'c:\\python38-32\\lib\\difflib.py', 'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\python38-32\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\python38-32\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\python38-32\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'c:\\python38-32\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\python38-32\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('_dummy_thread', 'c:\\python38-32\\lib\\_dummy_thread.py', 'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\python38-32\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('unittest.case', 'c:\\python38-32\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.util', 'c:\\python38-32\\lib\\unittest\\util.py', 'PYMODULE'),
  ('unittest.result', 'c:\\python38-32\\lib\\unittest\\result.py', 'PYMODULE'),
  ('numpy.testing._private',
   'c:\\python38-32\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest', 'c:\\python38-32\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.signals',
   'c:\\python38-32\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main', 'c:\\python38-32\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'c:\\python38-32\\lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'c:\\python38-32\\lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'c:\\python38-32\\lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.async_case',
   'c:\\python38-32\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio', 'c:\\python38-32\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\python38-32\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'c:\\python38-32\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\python38-32\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\python38-32\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\python38-32\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\python38-32\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\python38-32\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\python38-32\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams', 'c:\\python38-32\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.queues', 'c:\\python38-32\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'c:\\python38-32\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.trsock', 'c:\\python38-32\\lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'c:\\python38-32\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks', 'c:\\python38-32\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('concurrent.futures',
   'c:\\python38-32\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\python38-32\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\python38-32\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\python38-32\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'c:\\python38-32\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\python38-32\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', 'c:\\python38-32\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\python38-32\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\python38-32\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\python38-32\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\python38-32\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures', 'c:\\python38-32\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.events', 'c:\\python38-32\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\python38-32\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\python38-32\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\python38-32\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\python38-32\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\python38-32\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\python38-32\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\python38-32\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\python38-32\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\python38-32\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\python38-32\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\python38-32\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy._typing',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\python38-32\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\python38-32\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\python38-32\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\python38-32\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\python38-32\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\python38-32\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\python38-32\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\python38-32\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\python38-32\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\python38-32\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\python38-32\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\python38-32\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\python38-32\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\python38-32\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\python38-32\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\python38-32\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\python38-32\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\python38-32\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\python38-32\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'c:\\python38-32\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('dataclasses', 'c:\\python38-32\\lib\\dataclasses.py', 'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'c:\\python38-32\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\python38-32\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('pandas',
   'c:\\python38-32\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._typing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'c:\\python38-32\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.util.version',
   'c:\\python38-32\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.util',
   'c:\\python38-32\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'c:\\python38-32\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'c:\\python38-32\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'c:\\python38-32\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'c:\\python38-32\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'c:\\python38-32\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.io._util',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.util._str_methods',
   'c:\\python38-32\\lib\\site-packages\\pandas\\util\\_str_methods.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'c:\\python38-32\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.common',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'c:\\python38-32\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'c:\\python38-32\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('dateutil.parser',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('six', 'c:\\python38-32\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('dateutil.tz',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('fractions', 'c:\\python38-32\\lib\\fractions.py', 'PYMODULE'),
  ('dateutil._common',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('pytz',
   'c:\\python38-32\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'c:\\python38-32\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'c:\\python38-32\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'c:\\python38-32\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('jinja2',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('markupsafe',
   'c:\\python38-32\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'c:\\python38-32\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('jinja2.utils',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'c:\\python38-32\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   'c:\\python38-32\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('uuid', 'c:\\python38-32\\lib\\uuid.py', 'PYMODULE'),
  ('ctypes.util', 'c:\\python38-32\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'c:\\python38-32\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\python38-32\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\python38-32\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\python38-32\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\python38-32\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.common',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.base',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._config.config',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.window',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.series',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'c:\\python38-32\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3', 'c:\\python38-32\\lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'c:\\python38-32\\lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'c:\\python38-32\\lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('pandas.io.json',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'c:\\python38-32\\lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'c:\\python38-32\\lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\python38-32\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\python38-32\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\python38-32\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\python38-32\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'c:\\python38-32\\lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom', 'c:\\python38-32\\lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\python38-32\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\python38-32\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\python38-32\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\python38-32\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree', 'c:\\python38-32\\lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('pandas.io.xml',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'c:\\python38-32\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.html',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'c:\\python38-32\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   'c:\\python38-32\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'c:\\python38-32\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'c:\\python38-32\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'c:\\python38-32\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'c:\\python38-32\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'c:\\python38-32\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'c:\\python38-32\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'c:\\python38-32\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'c:\\python38-32\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'c:\\python38-32\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'c:\\python38-32\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'c:\\python38-32\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.api',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'c:\\python38-32\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._libs',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'c:\\python38-32\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'c:\\python38-32\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('datetime', 'c:\\python38-32\\lib\\datetime.py', 'PYMODULE'),
  ('pymysql',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.charset',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.converters',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql._auth',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.times',
   'c:\\python38-32\\lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('flask',
   'c:\\python38-32\\lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.wrappers',
   'c:\\python38-32\\lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'c:\\python38-32\\lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'c:\\python38-32\\lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('click',
   'c:\\python38-32\\lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click.utils',
   'c:\\python38-32\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click._compat',
   'c:\\python38-32\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('colorama',
   'c:\\python38-32\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'c:\\python38-32\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'c:\\python38-32\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'c:\\python38-32\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'c:\\python38-32\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'c:\\python38-32\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('click._winconsole',
   'c:\\python38-32\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   'c:\\python38-32\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'c:\\python38-32\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.parser',
   'c:\\python38-32\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.globals',
   'c:\\python38-32\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.formatting',
   'c:\\python38-32\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'c:\\python38-32\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   'c:\\python38-32\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   'c:\\python38-32\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('importlib_metadata',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'c:\\python38-32\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat',
   'c:\\python38-32\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp',
   'c:\\python38-32\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.glob',
   'c:\\python38-32\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'c:\\python38-32\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('click.core',
   'c:\\python38-32\\lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.shell_completion',
   'c:\\python38-32\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.types',
   'c:\\python38-32\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('werkzeug.utils',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.test',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.http',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('werkzeug.security',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('flask.sansio.app',
   'c:\\python38-32\\lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'c:\\python38-32\\lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.testing',
   'c:\\python38-32\\lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.sessions',
   'c:\\python38-32\\lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.json.tag',
   'c:\\python38-32\\lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('itsdangerous',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('click.testing',
   'c:\\python38-32\\lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('flask.logging',
   'c:\\python38-32\\lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('werkzeug.local',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('flask.json.provider',
   'c:\\python38-32\\lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'c:\\python38-32\\lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('flask.templating',
   'c:\\python38-32\\lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.signals',
   'c:\\python38-32\\lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('blinker',
   'c:\\python38-32\\lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker.base',
   'c:\\python38-32\\lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('blinker._utilities',
   'c:\\python38-32\\lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('flask.helpers',
   'c:\\python38-32\\lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.globals',
   'c:\\python38-32\\lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.ctx',
   'c:\\python38-32\\lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.config',
   'c:\\python38-32\\lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.blueprints',
   'c:\\python38-32\\lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.app',
   'c:\\python38-32\\lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.cli',
   'c:\\python38-32\\lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('rlcompleter', 'c:\\python38-32\\lib\\rlcompleter.py', 'PYMODULE'),
  ('flask.typing',
   'c:\\python38-32\\lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.json',
   'c:\\python38-32\\lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE')],
 [('python38.dll', 'c:\\python38-32\\python38.dll', 'BINARY'),
  ('numpy\\.libs\\libopenblas_v0.3.21-gcc_8_3_0.dll',
   'c:\\python38-32\\lib\\site-packages\\numpy\\.libs\\libopenblas_v0.3.21-gcc_8_3_0.dll',
   'BINARY'),
  ('select.pyd', 'c:\\python38-32\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'c:\\python38-32\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'c:\\python38-32\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'c:\\python38-32\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'c:\\python38-32\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'c:\\python38-32\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'c:\\python38-32\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'c:\\python38-32\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'c:\\python38-32\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'c:\\python38-32\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'c:\\python38-32\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'c:\\python38-32\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win32.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'c:\\python38-32\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'c:\\python38-32\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('numpy\\random\\mtrand.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\_philox.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\_generator.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\random\\_common.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win32.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\writers.cp38-win32.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\markupsafe\\_speedups.cp38-win32.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'c:\\python38-32\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('_elementtree.pyd', 'c:\\python38-32\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\testing.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\sparse.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\reshape.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\reduction.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\properties.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\parsers.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\ops.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\missing.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\json.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\join.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\interval.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\internals.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\indexing.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\index.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\hashing.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\groupby.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\arrays.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\algos.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_byteswap.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\sas\\_byteswap.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\tslib.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\lib.cp38-win32.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp38-win32.pyd',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\hashtable.cp38-win32.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'c:\\python38-32\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-1_1.dll', 'c:\\python38-32\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libssl-1_1.dll', 'c:\\python38-32\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'c:\\python38-32\\DLLs\\libffi-7.dll', 'BINARY'),
  ('sqlite3.dll', 'c:\\python38-32\\DLLs\\sqlite3.dll', 'BINARY'),
  ('pandas\\_libs\\window\\MSVCP140.dll',
   'c:\\python38-32\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY')],
 [],
 [],
 [('static\\css\\bootstrap.min.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\css\\bootstrap.min.css',
   'DATA'),
  ('static\\css\\daterangepicker.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\css\\daterangepicker.css',
   'DATA'),
  ('static\\css\\demo.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\css\\demo.css',
   'DATA'),
  ('static\\css\\desktop.ini',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\css\\desktop.ini',
   'DATA'),
  ('static\\css\\main.css',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\css\\main.css',
   'DATA'),
  ('static\\fireworks.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\fireworks.html',
   'DATA'),
  ('static\\index.html',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\index.html',
   'DATA'),
  ('static\\js\\bootstrap.bundle.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\bootstrap.bundle.min.js',
   'DATA'),
  ('static\\js\\daterangepicker.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\daterangepicker.min.js',
   'DATA'),
  ('static\\js\\echarts.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\echarts.min.js',
   'DATA'),
  ('static\\js\\jquery.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\jquery.min.js',
   'DATA'),
  ('static\\js\\moment.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\moment.min.js',
   'DATA'),
  ('static\\js\\xlsx.full.min.js',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\static\\js\\xlsx.full.min.js',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'c:\\python38-32\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'c:\\python38-32\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'c:\\python38-32\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\WHEEL',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\RECORD',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\LICENSE',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\METADATA',
   'c:\\python38-32\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\METADATA',
   'DATA'),
  ('flask-3.0.3.dist-info\\LICENSE.txt',
   'c:\\python38-32\\lib\\site-packages\\flask-3.0.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.0.6.dist-info\\WHEEL',
   'c:\\python38-32\\lib\\site-packages\\werkzeug-3.0.6.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.0.6.dist-info\\METADATA',
   'c:\\python38-32\\lib\\site-packages\\werkzeug-3.0.6.dist-info\\METADATA',
   'DATA'),
  ('blinker-1.8.2.dist-info\\WHEEL',
   'c:\\python38-32\\lib\\site-packages\\blinker-1.8.2.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.0.6.dist-info\\LICENSE.txt',
   'c:\\python38-32\\lib\\site-packages\\werkzeug-3.0.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.0.3.dist-info\\METADATA',
   'c:\\python38-32\\lib\\site-packages\\flask-3.0.3.dist-info\\METADATA',
   'DATA'),
  ('blinker-1.8.2.dist-info\\RECORD',
   'c:\\python38-32\\lib\\site-packages\\blinker-1.8.2.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.0.3.dist-info\\entry_points.txt',
   'c:\\python38-32\\lib\\site-packages\\flask-3.0.3.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-3.0.3.dist-info\\REQUESTED',
   'c:\\python38-32\\lib\\site-packages\\flask-3.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('blinker-1.8.2.dist-info\\INSTALLER',
   'c:\\python38-32\\lib\\site-packages\\blinker-1.8.2.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.0.6.dist-info\\INSTALLER',
   'c:\\python38-32\\lib\\site-packages\\werkzeug-3.0.6.dist-info\\INSTALLER',
   'DATA'),
  ('blinker-1.8.2.dist-info\\METADATA',
   'c:\\python38-32\\lib\\site-packages\\blinker-1.8.2.dist-info\\METADATA',
   'DATA'),
  ('flask-3.0.3.dist-info\\RECORD',
   'c:\\python38-32\\lib\\site-packages\\flask-3.0.3.dist-info\\RECORD',
   'DATA'),
  ('flask-3.0.3.dist-info\\WHEEL',
   'c:\\python38-32\\lib\\site-packages\\flask-3.0.3.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('flask-3.0.3.dist-info\\INSTALLER',
   'c:\\python38-32\\lib\\site-packages\\flask-3.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'c:\\python38-32\\lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('blinker-1.8.2.dist-info\\LICENSE.txt',
   'c:\\python38-32\\lib\\site-packages\\blinker-1.8.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.0.6.dist-info\\RECORD',
   'c:\\python38-32\\lib\\site-packages\\werkzeug-3.0.6.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\Code\\2.python\\3.WebService\\build\\app\\base_library.zip',
   'DATA')],
 [('ntpath', 'c:\\python38-32\\lib\\ntpath.py', 'PYMODULE'),
  ('keyword', 'c:\\python38-32\\lib\\keyword.py', 'PYMODULE'),
  ('sre_constants', 'c:\\python38-32\\lib\\sre_constants.py', 'PYMODULE'),
  ('posixpath', 'c:\\python38-32\\lib\\posixpath.py', 'PYMODULE'),
  ('warnings', 'c:\\python38-32\\lib\\warnings.py', 'PYMODULE'),
  ('stat', 'c:\\python38-32\\lib\\stat.py', 'PYMODULE'),
  ('codecs', 'c:\\python38-32\\lib\\codecs.py', 'PYMODULE'),
  ('collections.abc', 'c:\\python38-32\\lib\\collections\\abc.py', 'PYMODULE'),
  ('collections', 'c:\\python38-32\\lib\\collections\\__init__.py', 'PYMODULE'),
  ('sre_parse', 'c:\\python38-32\\lib\\sre_parse.py', 'PYMODULE'),
  ('copyreg', 'c:\\python38-32\\lib\\copyreg.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'c:\\python38-32\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'c:\\python38-32\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'c:\\python38-32\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'c:\\python38-32\\lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'c:\\python38-32\\lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'c:\\python38-32\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'c:\\python38-32\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'c:\\python38-32\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'c:\\python38-32\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'c:\\python38-32\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'c:\\python38-32\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'c:\\python38-32\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'c:\\python38-32\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'c:\\python38-32\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'c:\\python38-32\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'c:\\python38-32\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'c:\\python38-32\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'c:\\python38-32\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'c:\\python38-32\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'c:\\python38-32\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'c:\\python38-32\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'c:\\python38-32\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'c:\\python38-32\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem', 'c:\\python38-32\\lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'c:\\python38-32\\lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'c:\\python38-32\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'c:\\python38-32\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'c:\\python38-32\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'c:\\python38-32\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'c:\\python38-32\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'c:\\python38-32\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'c:\\python38-32\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'c:\\python38-32\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'c:\\python38-32\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_centeuro',
   'c:\\python38-32\\lib\\encodings\\mac_centeuro.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'c:\\python38-32\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'c:\\python38-32\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'c:\\python38-32\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'c:\\python38-32\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'c:\\python38-32\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'c:\\python38-32\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab', 'c:\\python38-32\\lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'c:\\python38-32\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'c:\\python38-32\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'c:\\python38-32\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'c:\\python38-32\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'c:\\python38-32\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'c:\\python38-32\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'c:\\python38-32\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'c:\\python38-32\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'c:\\python38-32\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'c:\\python38-32\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'c:\\python38-32\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'c:\\python38-32\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'c:\\python38-32\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'c:\\python38-32\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'c:\\python38-32\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'c:\\python38-32\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'c:\\python38-32\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'c:\\python38-32\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'c:\\python38-32\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'c:\\python38-32\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'c:\\python38-32\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'c:\\python38-32\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'c:\\python38-32\\lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'c:\\python38-32\\lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'c:\\python38-32\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'c:\\python38-32\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'c:\\python38-32\\lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312',
   'c:\\python38-32\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'c:\\python38-32\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'c:\\python38-32\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'c:\\python38-32\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'c:\\python38-32\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'c:\\python38-32\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'c:\\python38-32\\lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'c:\\python38-32\\lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'c:\\python38-32\\lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'c:\\python38-32\\lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'c:\\python38-32\\lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'c:\\python38-32\\lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'c:\\python38-32\\lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'c:\\python38-32\\lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'c:\\python38-32\\lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'c:\\python38-32\\lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'c:\\python38-32\\lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'c:\\python38-32\\lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'c:\\python38-32\\lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'c:\\python38-32\\lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'c:\\python38-32\\lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'c:\\python38-32\\lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'c:\\python38-32\\lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'c:\\python38-32\\lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'c:\\python38-32\\lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'c:\\python38-32\\lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'c:\\python38-32\\lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'c:\\python38-32\\lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'c:\\python38-32\\lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'c:\\python38-32\\lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'c:\\python38-32\\lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'c:\\python38-32\\lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258',
   'c:\\python38-32\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'c:\\python38-32\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'c:\\python38-32\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'c:\\python38-32\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'c:\\python38-32\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'c:\\python38-32\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'c:\\python38-32\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'c:\\python38-32\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'c:\\python38-32\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'c:\\python38-32\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'c:\\python38-32\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'c:\\python38-32\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'c:\\python38-32\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037', 'c:\\python38-32\\lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap',
   'c:\\python38-32\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'c:\\python38-32\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'c:\\python38-32\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'c:\\python38-32\\lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'c:\\python38-32\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'c:\\python38-32\\lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases',
   'c:\\python38-32\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'c:\\python38-32\\lib\\encodings\\__init__.py', 'PYMODULE'),
  ('heapq', 'c:\\python38-32\\lib\\heapq.py', 'PYMODULE'),
  ('functools', 'c:\\python38-32\\lib\\functools.py', 'PYMODULE'),
  ('enum', 'c:\\python38-32\\lib\\enum.py', 'PYMODULE'),
  ('reprlib', 'c:\\python38-32\\lib\\reprlib.py', 'PYMODULE'),
  ('linecache', 'c:\\python38-32\\lib\\linecache.py', 'PYMODULE'),
  ('weakref', 'c:\\python38-32\\lib\\weakref.py', 'PYMODULE'),
  ('types', 'c:\\python38-32\\lib\\types.py', 'PYMODULE'),
  ('_bootlocale', 'c:\\python38-32\\lib\\_bootlocale.py', 'PYMODULE'),
  ('operator', 'c:\\python38-32\\lib\\operator.py', 'PYMODULE'),
  ('_collections_abc', 'c:\\python38-32\\lib\\_collections_abc.py', 'PYMODULE'),
  ('_weakrefset', 'c:\\python38-32\\lib\\_weakrefset.py', 'PYMODULE'),
  ('abc', 'c:\\python38-32\\lib\\abc.py', 'PYMODULE'),
  ('sre_compile', 'c:\\python38-32\\lib\\sre_compile.py', 'PYMODULE'),
  ('genericpath', 'c:\\python38-32\\lib\\genericpath.py', 'PYMODULE'),
  ('locale', 'c:\\python38-32\\lib\\locale.py', 'PYMODULE'),
  ('io', 'c:\\python38-32\\lib\\io.py', 'PYMODULE'),
  ('traceback', 'c:\\python38-32\\lib\\traceback.py', 'PYMODULE'),
  ('os', 'c:\\python38-32\\lib\\os.py', 'PYMODULE'),
  ('re', 'c:\\python38-32\\lib\\re.py', 'PYMODULE')])
