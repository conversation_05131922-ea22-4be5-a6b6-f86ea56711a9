#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动打包脚本 - 包含版本管理功能
作者: AI Assistant
功能: 自动递增版本号并打包为exe文件
"""

import os
import sys
import json
import subprocess
from datetime import datetime

VERSION_FILE = 'version.json'

def load_version_info():
    """加载版本信息"""
    try:
        if os.path.exists(VERSION_FILE):
            with open(VERSION_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 默认版本信息
            default_version = {
                'version': 'v1.0.0',
                'release_date': datetime.now().strftime('%Y-%m-%d'),
                'build_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'build_count': 1
            }
            save_version_info(default_version)
            return default_version
    except Exception as e:
        print(f"加载版本信息失败: {str(e)}")
        return {
            'version': 'v1.0.0',
            'release_date': datetime.now().strftime('%Y-%m-%d'),
            'build_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'build_count': 1
        }

def save_version_info(version_info):
    """保存版本信息"""
    try:
        with open(VERSION_FILE, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)
        print(f"版本信息已保存: {version_info['version']}")
    except Exception as e:
        print(f"保存版本信息失败: {str(e)}")

def increment_version():
    """版本自动递增"""
    try:
        version_info = load_version_info()
        build_count = version_info.get('build_count', 1)
        
        # 递增构建次数
        build_count += 1
        
        # 生成新版本号 (主版本.次版本.构建次数)
        major = 1
        minor = 0
        new_version = f"v{major}.{minor}.{build_count}"
        
        # 更新版本信息
        version_info.update({
            'version': new_version,
            'release_date': datetime.now().strftime('%Y-%m-%d'),
            'build_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'build_count': build_count
        })
        
        save_version_info(version_info)
        print(f"✅ 版本已更新为: {new_version}")
        return version_info
    except Exception as e:
        print(f"❌ 版本递增失败: {str(e)}")
        return load_version_info()

def build_exe():
    """使用PyInstaller打包exe"""
    try:
        print("🚀 开始打包exe文件...")
        
        # PyInstaller命令
        cmd = [
            'pyinstaller',
            '--onefile',
            '--add-data', 'static;static',
            '--add-data', 'version.json;.',
            '--name', 'TestDataAnalysisSystem',
            '--icon', 'static/favicon.ico',  # 如果有图标文件
            'app.py'
        ]
        
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ exe文件打包成功!")
            print("📁 输出目录: dist/")
            return True
        else:
            print("❌ exe文件打包失败!")
            print("错误信息:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 测试数据分析系统 - 自动打包工具")
    print("=" * 50)
    
    # 1. 递增版本号
    print("\n📋 步骤1: 更新版本信息")
    version_info = increment_version()
    
    # 2. 打包exe
    print(f"\n📦 步骤2: 打包exe文件 (版本: {version_info['version']})")
    success = build_exe()
    
    # 3. 显示结果
    print("\n" + "=" * 50)
    if success:
        print("🎉 打包完成!")
        print(f"📌 当前版本: {version_info['version']}")
        print(f"📅 构建时间: {version_info['build_time']}")
        print("📁 exe文件位置: dist/TestDataAnalysisSystem.exe")
    else:
        print("💥 打包失败，请检查错误信息")
    print("=" * 50)

if __name__ == '__main__':
    main()
