# 测试数据分析系统 - 版本管理功能说明

## 🌟 新增功能

### 1. 网页图标 (Favicon)
- ✅ 添加了彩色地球图标，替换默认浏览器图标
- 🎨 使用SVG格式，支持高清显示
- 🌍 图标设计灵感来源于地球元素

### 2. 版本显示系统
- 📍 **位置**: 网页右上角
- 🎯 **样式**: 渐变色徽章，带动画效果
- 📊 **信息**: 显示当前版本号，点击查看详细信息

### 3. 版本详情弹窗
点击版本徽章可查看：
- 📌 当前版本号
- 📅 发布日期  
- ⏰ 构建时间
- 🏷️ 系统名称
- 👨‍💻 开发团队

### 4. 自动版本递增
- 🔄 每次打包exe时自动递增版本号
- 📈 版本格式: v主版本.次版本.构建次数 (如: v1.0.15)
- 💾 版本信息保存在 `version.json` 文件中

## 🚀 使用方法

### 开发模式
```bash
python app.py
```
- 开发模式下不会自动递增版本
- 可通过API手动递增: `POST /api/version/increment`

### 打包exe (推荐方式)
```bash
# 方式1: 使用自动打包脚本
python build_exe.py

# 方式2: 使用批处理文件
build.bat

# 方式3: 手动打包 (传统方式)
pyinstaller --onefile --add-data "static;static" --add-data "version.json;." app.py
```

## 📁 相关文件

### 新增文件
- `build_exe.py` - 自动打包脚本，包含版本管理
- `build.bat` - Windows批处理快速打包
- `version.json` - 版本信息存储文件
- `版本管理说明.md` - 本说明文档

### 修改文件
- `static/index.html` - 添加版本显示UI和图标
- `app.py` - 添加版本管理API

## 🔧 API接口

### 获取版本信息
```
GET /api/version
```
返回:
```json
{
  "version": "v1.0.15",
  "release_date": "2024-01-01",
  "build_time": "2024-01-01 12:00:00",
  "build_count": 15
}
```

### 手动递增版本
```
POST /api/version/increment
```
返回:
```json
{
  "success": true,
  "message": "版本已更新为 v1.0.16",
  "version_info": { ... }
}
```

## 🎨 UI特性

### 版本徽章样式
- 🌈 渐变背景色 (蓝紫色)
- ✨ 悬停动画效果
- 🔄 图标脉冲动画
- 🎯 毛玻璃效果

### 版本弹窗
- 📱 响应式设计
- 🎭 滑入动画
- 🖱️ 点击背景或ESC键关闭
- 📋 详细信息展示

## 📝 版本历史

- **v1.0.1** - 初始版本，基础功能
- **v1.0.x** - 功能迭代和优化
- **v1.0.latest** - 添加版本管理和图标功能

## 🔮 未来计划

- [ ] 版本更新日志功能
- [ ] 自动检查更新
- [ ] 版本回滚功能
- [ ] 更多图标主题选择

---

**开发团队**: IE Team  
**最后更新**: 2024年1月  
**技术栈**: Flask + Bootstrap + JavaScript
